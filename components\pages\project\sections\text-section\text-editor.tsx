"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Text } from "@prisma/client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Type,
  Heading1,
  Heading2,
  Heading3,
  Strikethrough,
  Code,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link,
  Unlink,
  Highlighter,
  Palette,
  Minus,
  MoreHorizontal,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type Props = {
  text: Text;
};

// Menu Bar Component
const MenuBar = ({
  editor,
  setIsMenuHovered,
}: {
  editor: any;
  setIsMenuHovered: (hovered: boolean) => void;
}) => {
  if (!editor) {
    return null;
  }

  const MenuButton = ({
    onClick,
    isActive,
    disabled,
    children,
    title,
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      variant="ghost"
      size="sm"
      className={`h-8 w-8 p-0 transition-colors ${
        isActive
          ? "bg-primary text-primary-foreground hover:bg-primary/90"
          : "hover:bg-muted"
      }`}
      onClick={onClick}
      disabled={disabled}
      title={title}
    >
      {children}
    </Button>
  );

  return (
    <div
      className="absolute -top-4 left-1/2 -translate-x-1/2 bg-background border border-border rounded-lg shadow-xl p-2 animate-in fade-in-0 zoom-in-95 duration-200 z-50"
      style={{
        width: "fit-content",
        maxWidth: "min(90vw, 800px)",
        minWidth: "300px",
      }}
      onMouseEnter={() => setIsMenuHovered(true)}
      onMouseLeave={() => setIsMenuHovered(false)}
    >
      {/* Menu content wrapper with proper overflow handling */}
      <div className="flex items-center gap-1 flex-wrap justify-center">
        {/* Text Formatting */}
        <div className="flex items-center gap-1">
          <MenuButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            isActive={editor.isActive("bold")}
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            isActive={editor.isActive("italic")}
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleStrike().run()}
            isActive={editor.isActive("strike")}
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleCode().run()}
            isActive={editor.isActive("code")}
            title="Inline Code"
          >
            <Code className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              // Underline extension would need to be installed
              if (editor.chain().focus().toggleUnderline) {
                editor.chain().focus().toggleUnderline().run();
              }
            }}
            isActive={editor.isActive("underline")}
            title="Underline (Extension Required)"
            disabled={!editor.chain().focus().toggleUnderline}
          >
            <Underline className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              // Highlight extension would need to be installed
              if (editor.chain().focus().toggleHighlight) {
                editor.chain().focus().toggleHighlight().run();
              }
            }}
            isActive={editor.isActive("highlight")}
            title="Highlight (Extension Required)"
            disabled={!editor.chain().focus().toggleHighlight}
          >
            <Highlighter className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Text Alignment */}
        <div className="flex items-center gap-1">
          <MenuButton
            onClick={() => {
              // TextAlign extension would need to be installed
              const setTextAlign = (editor as any).chain().focus().setTextAlign;
              if (setTextAlign) {
                setTextAlign("left").run();
              }
            }}
            isActive={editor.isActive({ textAlign: "left" })}
            title="Align Left (Extension Required)"
            disabled={!(editor as any).chain().focus().setTextAlign}
          >
            <AlignLeft className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const setTextAlign = (editor as any).chain().focus().setTextAlign;
              if (setTextAlign) {
                setTextAlign("center").run();
              }
            }}
            isActive={editor.isActive({ textAlign: "center" })}
            title="Align Center (Extension Required)"
            disabled={!(editor as any).chain().focus().setTextAlign}
          >
            <AlignCenter className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const setTextAlign = (editor as any).chain().focus().setTextAlign;
              if (setTextAlign) {
                setTextAlign("right").run();
              }
            }}
            isActive={editor.isActive({ textAlign: "right" })}
            title="Align Right (Extension Required)"
            disabled={!(editor as any).chain().focus().setTextAlign}
          >
            <AlignRight className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const setTextAlign = (editor as any).chain().focus().setTextAlign;
              if (setTextAlign) {
                setTextAlign("justify").run();
              }
            }}
            isActive={editor.isActive({ textAlign: "justify" })}
            title="Justify (Extension Required)"
            disabled={!(editor as any).chain().focus().setTextAlign}
          >
            <AlignJustify className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Headings */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2 text-sm">
                {editor.isActive("heading", { level: 1 })
                  ? "H1"
                  : editor.isActive("heading", { level: 2 })
                  ? "H2"
                  : editor.isActive("heading", { level: 3 })
                  ? "H3"
                  : "P"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().setParagraph().run()}
                className={editor.isActive("paragraph") ? "bg-accent" : ""}
              >
                Paragraph
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 1 }).run()
                }
                className={
                  editor.isActive("heading", { level: 1 }) ? "bg-accent" : ""
                }
              >
                <Heading1 className="h-4 w-4 mr-2" />
                Heading 1
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 2 }).run()
                }
                className={
                  editor.isActive("heading", { level: 2 }) ? "bg-accent" : ""
                }
              >
                <Heading2 className="h-4 w-4 mr-2" />
                Heading 2
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 3 }).run()
                }
                className={
                  editor.isActive("heading", { level: 3 }) ? "bg-accent" : ""
                }
              >
                <Heading3 className="h-4 w-4 mr-2" />
                Heading 3
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Lists */}
        <div className="flex items-center gap-1">
          <MenuButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive("bulletList")}
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive("orderedList")}
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            isActive={editor.isActive("blockquote")}
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* Links and Special Elements */}
        <div className="flex items-center gap-1">
          <MenuButton
            onClick={() => {
              const url = window.prompt("Enter URL:");
              if (url) {
                const setLink = (editor as any).chain().focus().setLink;
                if (setLink) {
                  setLink({ href: url }).run();
                }
              }
            }}
            isActive={editor.isActive("link")}
            title="Add Link (Extension Required)"
            disabled={!(editor as any).chain().focus().setLink}
          >
            <Link className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const unsetLink = (editor as any).chain().focus().unsetLink;
              if (unsetLink) {
                unsetLink().run();
              }
            }}
            disabled={
              !editor.isActive("link") ||
              !(editor as any).chain().focus().unsetLink
            }
            title="Remove Link (Extension Required)"
          >
            <Unlink className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => {
              const setHorizontalRule = (editor as any)
                .chain()
                .focus().setHorizontalRule;
              if (setHorizontalRule) {
                setHorizontalRule().run();
              }
            }}
            title="Horizontal Rule (Extension Required)"
            disabled={!(editor as any).chain().focus().setHorizontalRule}
          >
            <Minus className="h-4 w-4" />
          </MenuButton>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* More Options */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                title="More Options"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().clearNodes().run()}
              >
                <Palette className="h-4 w-4 mr-2" />
                Clear Formatting
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().selectAll().run()}
              >
                Select All
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  const content = editor.getHTML();
                  navigator.clipboard.writeText(content);
                }}
              >
                Copy HTML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="w-px h-6 bg-border mx-1" />

        {/* History */}
        <div className="flex items-center gap-1">
          <MenuButton
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            title="Undo (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </MenuButton>

          <MenuButton
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            title="Redo (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </MenuButton>
        </div>
      </div>
    </div>
  );
};

export default function TextEditor({ text: { content } }: Props) {
  const [isEditing, setIsEditing] = useState(false);
  const [isMenuHovered, setIsMenuHovered] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      // Note: Additional extensions like TextAlign, Link, Underline, Highlight, HorizontalRule
      // would need to be installed separately from @tiptap packages
      // For now, we'll use the basic StarterKit functionality
    ],
    content: content || "",
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class:
          "prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-6 rounded-lg transition-all duration-200",
      },
      handleKeyDown: (_view, event) => {
        // Custom keyboard shortcuts
        if (event.ctrlKey || event.metaKey) {
          switch (event.key) {
            case "b":
              event.preventDefault();
              editor?.chain().focus().toggleBold().run();
              return true;
            case "i":
              event.preventDefault();
              editor?.chain().focus().toggleItalic().run();
              return true;
            case "u":
              event.preventDefault();
              editor?.chain().focus().toggleUnderline?.().run();
              return true;
            case "`":
              event.preventDefault();
              editor?.chain().focus().toggleCode().run();
              return true;
            case "k":
              event.preventDefault();
              const url = window.prompt("Enter URL:");
              if (url) {
                editor?.chain().focus().setLink?.({ href: url }).run();
              }
              return true;
            case "e":
              if (event.shiftKey) {
                event.preventDefault();
                const setTextAlign = (editor as any)
                  ?.chain()
                  .focus().setTextAlign;
                if (setTextAlign) {
                  setTextAlign("center").run();
                }
                return true;
              }
              break;
            case "l":
              if (event.shiftKey) {
                event.preventDefault();
                const setTextAlign = (editor as any)
                  ?.chain()
                  .focus().setTextAlign;
                if (setTextAlign) {
                  setTextAlign("left").run();
                }
                return true;
              }
              break;
            case "r":
              if (event.shiftKey) {
                event.preventDefault();
                const setTextAlign = (editor as any)
                  ?.chain()
                  .focus().setTextAlign;
                if (setTextAlign) {
                  setTextAlign("right").run();
                }
                return true;
              }
              break;
            case "\\":
              event.preventDefault();
              editor?.chain().focus().clearNodes().run();
              return true;
          }
        }
        return false;
      },
    },
    onFocus: () => setIsEditing(true),
    onBlur: () => {
      // Delay blur to allow menu interactions, but check if menu is hovered
      setTimeout(() => {
        if (!isMenuHovered) {
          setIsEditing(false);
        }
      }, 200);
    },
    onCreate: ({ editor }) => {
      // Auto-save functionality can be added here
      console.log("Editor created:", editor);
    },
    onUpdate: ({ editor }) => {
      // Auto-save functionality can be added here
      console.log("Content updated:", editor.getHTML());
    },
  });

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-muted rounded w-5/6"></div>
      </div>
    );
  }

  return (
    <div
      className="group/editor relative"
      style={{
        paddingTop: isEditing ? "4rem" : "0",
        transition: "padding-top 200ms ease",
      }}
    >
      {/* Floating menu bar - only visible when editing */}
      {isEditing && (
        <MenuBar editor={editor} setIsMenuHovered={setIsMenuHovered} />
      )}

      {/* Editor content */}
      <div
        className={`relative transition-all duration-200 ${
          isEditing
            ? "ring-2 ring-primary/20 bg-background"
            : "hover:bg-muted/20"
        } rounded-lg`}
      >
        {!isEditing && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/editor:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-background/90 backdrop-blur-sm border border-border rounded-lg px-3 py-1 flex items-center gap-2 text-sm text-muted-foreground">
              <Type className="h-4 w-4" />
              Click to edit text
            </div>
          </div>
        )}
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}
