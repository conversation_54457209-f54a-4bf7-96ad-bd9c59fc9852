"use server";
import React from "react";
import Logo from "./logo";
import Container from "../core/container";
import { currentUser } from "@clerk/nextjs/server";
import { updateExistingUserId } from "@/actions/user";
import AuthButtons from "./auth-buttons";

type Props = {};

const Header = async (props: Props) => {
  //we need to update existing user id to clerk id based on email using prisma

  const user = await currentUser();

  await updateExistingUserId(user);

  return (
    <header className="sticky top-0 z-50 bg-background">
      <Container className="flex justify-between items-center">
        <Logo />
        <AuthButtons />
      </Container>
    </header>
  );
};

export default Header;
